import { <PERSON>, Get, Param, ParseIntPipe, Res, NotFoundException } from '@nestjs/common';
import { Response } from 'express';
import { join } from 'path';
import * as fs from 'fs';
import { ImagesService } from './images.service';
import { ImageResponseDto } from './dto/image.dto';

@Controller('api/images')
export class ImagesController {
  constructor(private readonly imagesService: ImagesService) {}

  @Get()
  async findAll(): Promise<{ success: boolean; data: ImageResponseDto[] }> {
    try {
      const images = await this.imagesService.findAll();
      return {
        success: true,
        data: images,
      };
    } catch (error) {
      console.error('获取图片列表失败:', error);
      throw error;
    }
  }

  @Get(':id/info')
  async getImageInfo(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ success: boolean; data: ImageResponseDto }> {
    try {
      const imageInfo = await this.imagesService.getImageUrl(id);
      return {
        success: true,
        data: imageInfo,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`获取图片信息失败:`, error);
      throw error;
    }
  }

  @Get(':id')
  async getImage(
    @Param('id', ParseIntPipe) id: number,
    @Res() res: Response,
  ) {
    try {
      const imageInfo = await this.imagesService.findOne(id);
      
      if (!imageInfo) {
        throw new NotFoundException('图片不存在');
      }
      
      const imagePath = join(process.cwd(), imageInfo.filePath, imageInfo.fileName);
      
      // 检查文件是否存在
      if (!fs.existsSync(imagePath)) {
        throw new NotFoundException('图片文件不存在');
      }
      
      // 设置适当的Content-Type
      res.setHeader('Content-Type', imageInfo.fileType || 'image/jpeg');
      
      // 发送文件
      return res.sendFile(imagePath);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('获取图片失败:', error);
      throw error;
    }
  }
}

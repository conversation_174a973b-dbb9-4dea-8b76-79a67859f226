import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('news')
export class News {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 512 })
  title: string;

  @Column({ length: 512, nullable: true })
  subtitle: string;

  @Column({ length: 255, nullable: true })
  author: string;

  @Column({ name: 'source_document', length: 255, nullable: true })
  sourceDocument: string;

  @Column({ name: 'source_filename', length: 255 })
  sourceFilename: string;

  @Column({ name: 'content', type: 'longtext' })
  content: string;

  @Column({ length: 255, nullable: true })
  category: string;

  @Column({ type: 'json', nullable: true })
  tags: any;

  @Column({ name: 'publication_info', length: 255, nullable: true })
  publicationInfo: string;

  @Column({ name: 'cover_image_id', nullable: true })
  coverImageId: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}

import { Injectable, OnModuleInit, RequestMethod } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { DiscoveryService, MetadataScanner, Reflector } from '@nestjs/core';
import { PATH_METADATA, METHOD_METADATA } from '@nestjs/common/constants';

@Injectable()
export class HealthService implements OnModuleInit {
  private routes: any[] = [];

  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
    private readonly discoveryService: DiscoveryService,
    private readonly metadataScanner: MetadataScanner,
    private readonly reflector: Reflector,
  ) {}

  onModuleInit() {
    this.scanRoutes();
  }

  private scanRoutes() {
    const controllers = this.discoveryService.getControllers();

    controllers.forEach(wrapper => {
      const { instance } = wrapper;
      if (!instance) return;

      const prototype = Object.getPrototypeOf(instance);
      const controllerPath = this.reflector.get(PATH_METADATA, instance.constructor);

      const methodNames = this.metadataScanner.getAllMethodNames(prototype);

      methodNames.forEach(methodName => {
        const method = prototype[methodName];
        const path = this.reflector.get(PATH_METADATA, method);
        const requestMethod = this.reflector.get(METHOD_METADATA, method);

        if (path !== undefined && requestMethod !== undefined) {
          const fullPath = controllerPath ? `/${controllerPath}/${path}`.replace(/\/\//g, '/') : `/${path}`;

          // 将 RequestMethod 枚举值转换为字符串
          let methodStr = 'UNKNOWN';
          switch (requestMethod) {
            case RequestMethod.GET:
              methodStr = 'GET';
              break;
            case RequestMethod.POST:
              methodStr = 'POST';
              break;
            case RequestMethod.PUT:
              methodStr = 'PUT';
              break;
            case RequestMethod.DELETE:
              methodStr = 'DELETE';
              break;
            case RequestMethod.PATCH:
              methodStr = 'PATCH';
              break;
            case RequestMethod.ALL:
              methodStr = 'ALL';
              break;
            case RequestMethod.OPTIONS:
              methodStr = 'OPTIONS';
              break;
            case RequestMethod.HEAD:
              methodStr = 'HEAD';
              break;
          }

          this.routes.push({
            path: fullPath.replace(/^\/?/, '/'),
            method: methodStr,
            controller: instance.constructor.name,
            handler: method.name
          });
        }
      });
    });

    // 按路径排序
    this.routes.sort((a, b) => a.path.localeCompare(b.path));
  }

  getAllRoutes() {
    // 先按原样返回所有路由
    const allRoutes = this.routes.map(route => ({
      path: route.path,
      method: route.method,
      controller: route.controller,
      handler: route.handler
    }));

    // 按模块分类
    const routesByModule = {};

    allRoutes.forEach(route => {
      // 从控制器名称中提取模块名
      let moduleName = 'Other';

      if (route.controller.includes('Controller')) {
        // 提取模块名称
        if (route.controller === 'AppController') {
          moduleName = 'App';
        } else if (route.controller.startsWith('Health')) {
          moduleName = 'Health';
        } else if (route.controller.startsWith('News')) {
          moduleName = 'News';
        } else if (route.controller.startsWith('Party')) {
          moduleName = 'Party';
        } else {
          // 尝试从控制器名称中提取模块名
          const match = route.controller.match(/^(.+?)Controller$/);
          if (match) {
            moduleName = match[1];
          }
        }
      }

      // 确保模块存在于分类对象中
      if (!routesByModule[moduleName]) {
        routesByModule[moduleName] = [];
      }

      // 添加路由到对应模块
      routesByModule[moduleName].push(route);
    });

    return {
      all: allRoutes,
      byModule: routesByModule
    };
  }

  async checkDatabase(): Promise<boolean> {
    try {
      // 检查数据库连接
      const result = await this.dataSource.query('SELECT 1');
      return result && result.length > 0;
    } catch (error) {
      console.error('数据库连接检查失败:', error);
      return false;
    }
  }
}

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { HttpExceptionFilter, AllExceptionsFilter } from '../common/filters/http-exception.filter';
import { envConfig } from '../config/env.config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    cors: true, // 开启跨域访问
  });

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
  }));

  // 全局异常过滤器
  app.useGlobalFilters(
    new HttpExceptionFilter(),
    new AllExceptionsFilter(),
  );

  // 设置全局前缀
  if (envConfig.apiPrefix) {
    app.setGlobalPrefix(envConfig.apiPrefix);
  }

  // 启动服务
  const port = envConfig.port;
  await app.listen(port);

  console.log(`服务启动成功，运行在 http://localhost:${port}/`);
}

bootstrap();

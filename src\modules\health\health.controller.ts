import { Controller, Get, HttpCode, HttpStatus } from '@nestjs/common';
import { HealthService } from './health.service';

@Controller('api/health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  async check() {
    const dbStatus = await this.healthService.checkDatabase();

    if (dbStatus) {
      return {
        success: true,
        message: 'API服务正常运行',
        database: '数据库连接正常'
      };
    } else {
      return {
        success: false,
        message: 'API服务正常运行',
        database: '数据库连接异常'
      };
    }
  }

  @Get('routes')
  @HttpCode(HttpStatus.OK)
  getAllRoutes() {
    const routesData = this.healthService.getAllRoutes();
    return {
      success: true,
      data: routesData,
      total: {
        all: routesData.all.length,
        byModule: Object.keys(routesData.byModule).reduce((acc, moduleName) => {
          acc[moduleName] = routesData.byModule[moduleName].length;
          return acc;
        }, {})
      }
    };
  }
}

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Image } from './entities/image.entity';
import { ImageDto, ImageResponseDto } from './dto/image.dto';

@Injectable()
export class ImagesService {
  constructor(
    @InjectRepository(Image)
    private imagesRepository: Repository<Image>,
  ) {}

  async findAll(): Promise<ImageResponseDto[]> {
    try {
      const images = await this.imagesRepository.find();
      return images.map(image => this.mapToResponseDto(image));
    } catch (error) {
      console.error('获取图片列表失败:', error);
      throw error;
    }
  }

  async findOne(id: number): Promise<ImageDto> {
    try {
      const image = await this.imagesRepository.findOne({ where: { id } });

      if (!image) {
        throw new NotFoundException(`图片ID ${id} 不存在`);
      }

      return image;
    } catch (error) {
      console.error(`获取图片ID ${id} 失败:`, error);
      throw error;
    }
  }

  async getImageUrl(id: number): Promise<ImageResponseDto> {
    try {
      const image = await this.findOne(id);
      return this.mapToResponseDto(image);
    } catch (error) {
      console.error(`获取图片URL失败:`, error);
      throw error;
    }
  }

  private mapToResponseDto(image: Image | ImageDto): ImageResponseDto {
    return {
      id: image.id,
      url: `${image.filePath}${image.fileName}`,
      title: image.title,
      description: image.description,
      altText: image.altText,
      width: image.width,
      height: image.height,
      uploadTime: image.uploadTime,
    };
  }
}

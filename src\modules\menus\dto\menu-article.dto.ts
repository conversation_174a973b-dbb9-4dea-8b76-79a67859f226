import { IsNotEmpty, IsOptional, IsNumber, IsString } from 'class-validator';

export class MenuArticleDto {
  id: number;
  menuId: number;
  title: string;
  subtitle?: string;
  content?: string;
  author?: string;
  coverImageId?: number;
  createdAt: Date;
  updatedAt: Date;
}

// 不包含content字段的DTO，用于列表展示
export class MenuArticleListDto {
  id: number;
  menuId: number;
  title: string;
  subtitle?: string;
  author?: string;
  coverImageId?: number;
  createdAt: Date;
  updatedAt: Date;
}

export class CreateMenuArticleDto {
  @IsNotEmpty()
  @IsNumber()
  menuId: number;

  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  subtitle?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsString()
  author?: string;

  @IsOptional()
  @IsNumber()
  coverImageId?: number;
}

export class UpdateMenuArticleDto {
  @IsOptional()
  @IsNumber()
  menuId?: number;

  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  subtitle?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsString()
  author?: string;

  @IsOptional()
  @IsNumber()
  coverImageId?: number;
}

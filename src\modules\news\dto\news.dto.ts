export class NewsDto {
  id: number;
  title: string;
  subtitle?: string;
  author?: string;
  sourceDocument?: string;
  sourceFilename: string;
  content: string;
  category?: string;
  tags?: any;
  publicationInfo?: string;
  coverImageId?: number;
  createdAt: Date;
  updatedAt: Date;
}

export class NewsListItemDto {
  id: number;
  title: string;
  subtitle?: string;
  author?: string;
  category?: string;
  coverImageId?: number;
  createdAt: Date;
}

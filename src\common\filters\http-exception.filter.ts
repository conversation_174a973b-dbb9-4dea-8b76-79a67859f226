import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();

    const errorResponse = {
      success: false,
      path: request.url,
      timestamp: new Date().toISOString(),
      ...(typeof exceptionResponse === 'object'
        ? exceptionResponse
        : { message: exceptionResponse }),
    };

    response.status(status).json(errorResponse);
  }
}

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // 数据库连接错误
    if (exception instanceof Error &&
        ['ECONNRESET', 'ECONNREFUSED', 'PROTOCOL_CONNECTION_LOST'].includes((exception as any).code)) {
      return response.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        message: '数据库连接暂时不可用，请稍后再试',
        path: request.url,
        timestamp: new Date().toISOString(),
        error: process.env.NODE_ENV === 'development' ? exception.message : undefined
      });
    }

    // 数据库查询错误
    if (exception instanceof Error && (exception as any).sql) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: '数据库查询错误',
        path: request.url,
        timestamp: new Date().toISOString(),
        error: process.env.NODE_ENV === 'development' ? exception.message : undefined
      });
    }

    // 默认服务器错误
    const status = exception instanceof HttpException
      ? exception.getStatus()
      : HttpStatus.INTERNAL_SERVER_ERROR;

    console.error('详细错误信息:', exception);

    response.status(status).json({
      success: false,
      message: '服务器内部错误',
      path: request.url,
      timestamp: new Date().toISOString(),
      error: exception instanceof Error ? exception.message : String(exception),
      stack: exception instanceof Error ? exception.stack : undefined
    });
  }
}

import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity('menus')
export class Menu {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'parent_id', nullable: true })
  parentId: number;

  @Column()
  level: number;

  @Column({ length: 50 })
  name: string;

  @Column({ name: 'sort_order' })
  sortOrder: number;

  @Column({ name: 'is_deleted', default: 0 })
  isDeleted: number;
}

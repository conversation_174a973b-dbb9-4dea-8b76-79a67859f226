import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGeneratedColumn, CreateDateColumn, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { PartyArticle } from './party-article.entity';

@Entity('party_categories')
export class PartyCategory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ name: 'parent_id', nullable: true })
  parentId: number;

  @Column()
  level: number;

  @Column({ name: 'sort_order' })
  sortOrder: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => PartyCategory, category => category.children)
  @JoinColumn({ name: 'parent_id' })
  parent: PartyCategory;

  @OneToMany(() => PartyCategory, category => category.parent)
  children: PartyCategory[];

  @OneToMany(() => PartyArticle, article => article.category)
  articles: PartyArticle[];
}

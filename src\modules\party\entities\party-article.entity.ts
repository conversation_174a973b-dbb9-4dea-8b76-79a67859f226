import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGeneratedColumn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { PartyCategory } from './party-category.entity';

@Entity('party_articles')
export class PartyArticle {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'category_id' })
  categoryId: number;

  @Column()
  title: string;

  @Column({ type: 'text', nullable: true })
  content: string;

  @Column({ name: 'cover_image_id', nullable: true })
  coverImageId: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => PartyCategory, category => category.articles)
  @JoinColumn({ name: 'category_id' })
  category: PartyCategory;
}

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { News } from './entities/news.entity';
import { NewsDto, NewsListItemDto } from './dto/news.dto';

@Injectable()
export class NewsService {
  constructor(
    @InjectRepository(News)
    private newsRepository: Repository<News>,
  ) {}

  async findAll(): Promise<NewsListItemDto[]> {
    try {
      const news = await this.newsRepository.find({
        select: ['id', 'title', 'subtitle', 'author', 'category', 'coverImageId', 'createdAt'],
        order: { createdAt: 'DESC' },
      });
      return news;
    } catch (error) {
      console.error('获取新闻列表失败:', error);
      throw error;
    }
  }

  async findOne(id: number): Promise<NewsDto> {
    try {
      const news = await this.newsRepository.findOne({ where: { id } });

      if (!news) {
        throw new NotFoundException(`新闻ID ${id} 不存在`);
      }

      return news;
    } catch (error) {
      console.error(`获取新闻ID ${id} 失败:`, error);
      throw error;
    }
  }
}

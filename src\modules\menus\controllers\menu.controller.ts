import { Controller, Get } from '@nestjs/common';
import { MenuService } from '../services/menu.service';
import { MenuDto, MenuTreeDto } from '../dto/menu.dto';

@Controller('api/menus')
export class MenuController {
  constructor(private readonly menuService: MenuService) {}

  @Get()
  async findAll(): Promise<{ success: boolean; data: MenuDto[] }> {
    try {
      const menus = await this.menuService.findAll();
      return {
        success: true,
        data: menus,
      };
    } catch (error) {
      console.error('获取菜单列表失败:', error);
      throw error;
    }
  }

  @Get('tree')
  async getMenuTree(): Promise<{ success: boolean; data: MenuTreeDto[] }> {
    try {
      const menuTree = await this.menuService.getMenuTree();
      return {
        success: true,
        data: menuTree,
      };
    } catch (error) {
      console.error('获取菜单树失败:', error);
      throw error;
    }
  }
}

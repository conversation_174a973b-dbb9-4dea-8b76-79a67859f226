export class ImageDto {
  id: number;
  filePath: string;
  fileName: string;
  fileSize?: number;
  fileType?: string;
  width?: number;
  height?: number;
  title?: string;
  description?: string;
  altText?: string;
  uploadTime: Date;
  updatedAt: Date;
}

export class ImageResponseDto {
  id: number;
  url: string;
  title?: string;
  description?: string;
  altText?: string;
  width?: number;
  height?: number;
  uploadTime: Date;
}

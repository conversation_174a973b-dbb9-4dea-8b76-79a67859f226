import { Controller, Get, Param, ParseIntPipe, NotFoundException } from '@nestjs/common';
import { PartyCategoryService } from '../services/party-category.service';
import { PartyArticleService } from '../services/party-article.service';
import { PartyCategoryDto, PartyCategoryTreeDto } from '../dto/party-category.dto';
import { PartyArticleListItemDto } from '../dto/party-article.dto';

@Controller('api/categories')
export class PartyCategoryController {
  constructor(
    private readonly partyCategoryService: PartyCategoryService,
    private readonly partyArticleService: PartyArticleService,
  ) {}

  @Get()
  async findAll(): Promise<{ success: boolean; data: PartyCategoryDto[] }> {
    try {
      const categories = await this.partyCategoryService.findAll();
      return {
        success: true,
        data: categories,
      };
    } catch (error) {
      console.error('获取分类列表失败:', error);
      throw error;
    }
  }

  @Get('tree')
  async getCategoryTree(): Promise<{ success: boolean; data: PartyCategoryTreeDto[] }> {
    try {
      const categoryTree = await this.partyCategoryService.getCategoryTree();
      return {
        success: true,
        data: categoryTree,
      };
    } catch (error) {
      console.error('获取分类树失败:', error);
      throw error;
    }
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ success: boolean; data: PartyCategoryDto }> {
    try {
      const category = await this.partyCategoryService.findOne(id);

      return {
        success: true,
        data: category,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`获取分类详情失败:`, error);
      throw error;
    }
  }

  @Get(':categoryId/articles')
  async findArticlesByCategoryId(
    @Param('categoryId', ParseIntPipe) categoryId: number,
  ): Promise<{ success: boolean; data: PartyArticleListItemDto[] }> {
    try {
      const articles = await this.partyArticleService.findByCategoryId(categoryId);
      return {
        success: true,
        data: articles,
      };
    } catch (error) {
      console.error(`获取分类ID ${categoryId} 的文章列表失败:`, error);
      throw error;
    }
  }
}

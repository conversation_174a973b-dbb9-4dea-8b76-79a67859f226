version: '3.8'

services:
  program-app:
    build:
        context: .
        dockerfile: Dockerfile
    image: program-image
    container_name: program-api
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=Yzz520530
      - DB_NAME=program
    volumes:
      - ./dist:/express/dist:rw
    depends_on:
      - mysql
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - app-network

  mysql:
    image: mysql:8.0
    container_name: express-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=Yzz520530
      - MYSQL_DATABASE=program
      - MYSQL_USER=liuliang520500
      - MYSQL_PASSWORD=Yzz520530
    volumes:
      - mysql-data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./mysql/conf:/etc/mysql/conf.d
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "Yzz520530"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  mysql-data:




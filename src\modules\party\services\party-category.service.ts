import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PartyCategory } from '../entities/party-category.entity';
import { PartyCategoryDto, PartyCategoryTreeDto } from '../dto/party-category.dto';

@Injectable()
export class PartyCategoryService {
  constructor(
    @InjectRepository(PartyCategory)
    private partyCategoryRepository: Repository<PartyCategory>,
  ) {}

  async findAll(): Promise<PartyCategoryDto[]> {
    try {
      const categories = await this.partyCategoryRepository.find({
        order: { level: 'ASC', sortOrder: 'ASC', id: 'ASC' },
      });
      return categories;
    } catch (error) {
      console.error('获取分类列表失败:', error);
      return [];
    }
  }

  async getCategoryTree(): Promise<PartyCategoryTreeDto[]> {
    try {
      // 获取所有分类
      const allCategories = await this.partyCategoryRepository.find({
        order: { level: 'ASC', sortOrder: 'ASC', id: 'ASC' },
      });

      // 构建分类树
      const categoryTree = this.buildCategoryTree(allCategories);

      return categoryTree;
    } catch (error) {
      console.error('获取分类树失败:', error);
      return [];
    }
  }

  private buildCategoryTree(categories: PartyCategory[], parentId: number | null = null): PartyCategoryTreeDto[] {
    const result: PartyCategoryTreeDto[] = [];

    // 找出当前层级的所有分类
    const currentLevelCategories = categories.filter(category => category.parentId === parentId);

    // 为每个分类添加子分类
    currentLevelCategories.forEach(category => {
      const categoryWithChildren: PartyCategoryTreeDto = {
        ...category,
        children: this.buildCategoryTree(categories, category.id)
      };

      result.push(categoryWithChildren);
    });

    return result;
  }

  async findOne(id: number): Promise<PartyCategoryDto> {
    try {
      const category = await this.partyCategoryRepository.findOne({ where: { id } });

      if (!category) {
        throw new NotFoundException(`分类ID ${id} 不存在`);
      }

      return category;
    } catch (error) {
      console.error(`获取分类ID ${id} 失败:`, error);
      throw error;
    }
  }
}

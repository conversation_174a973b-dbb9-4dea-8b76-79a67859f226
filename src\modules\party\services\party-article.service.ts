import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PartyArticle } from '../entities/party-article.entity';
import { PartyArticleDto, PartyArticleListItemDto } from '../dto/party-article.dto';

@Injectable()
export class PartyArticleService {
  constructor(
    @InjectRepository(PartyArticle)
    private partyArticleRepository: Repository<PartyArticle>,
  ) {}

  async findAll(): Promise<PartyArticleListItemDto[]> {
    try {
      const articles = await this.partyArticleRepository.find({
        select: ['id', 'categoryId', 'title', 'coverImageId', 'createdAt'],
        order: { createdAt: 'DESC' },
      });
      return articles;
    } catch (error) {
      console.error('获取党建文章列表失败:', error);
      return [];
    }
  }

  async findOne(id: number): Promise<PartyArticleDto> {
    try {
      const article = await this.partyArticleRepository.findOne({ where: { id } });

      if (!article) {
        throw new NotFoundException(`党建文章ID ${id} 不存在`);
      }

      return article;
    } catch (error) {
      console.error(`获取党建文章ID ${id} 失败:`, error);
      throw error;
    }
  }

  async findByCategoryId(categoryId: number): Promise<PartyArticleListItemDto[]> {
    try {
      const articles = await this.partyArticleRepository.find({
        select: ['id', 'categoryId', 'title', 'coverImageId', 'createdAt'],
        where: { categoryId },
        order: { createdAt: 'DESC' },
      });
      return articles;
    } catch (error) {
      console.error(`获取分类ID ${categoryId} 的文章列表失败:`, error);
      return [];
    }
  }
}

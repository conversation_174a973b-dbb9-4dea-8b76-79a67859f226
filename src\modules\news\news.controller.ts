import { Controller, Get, Param, ParseIntPipe, NotFoundException } from '@nestjs/common';
import { NewsService } from './news.service';
import { NewsDto, NewsListItemDto } from './dto/news.dto';

@Controller('api/news')
export class NewsController {
  constructor(private readonly newsService: NewsService) {}

  @Get()
  async findAll(): Promise<{ success: boolean; data: NewsListItemDto[] }> {
    try {
      const news = await this.newsService.findAll();
      return {
        success: true,
        data: news,
      };
    } catch (error) {
      console.error('获取新闻列表失败:', error);
      throw error;
    }
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ success: boolean; data: NewsDto }> {
    try {
      const news = await this.newsService.findOne(id);
      
      return {
        success: true,
        data: news,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`获取新闻详情失败:`, error);
      throw error;
    }
  }
}

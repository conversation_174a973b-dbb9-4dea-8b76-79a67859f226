import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { NewsModule } from '../modules/news/news.module';
import { PartyModule } from '../modules/party/party.module';
import { HealthModule } from '../modules/health/health.module';
import { MenusModule } from '../modules/menus/menus.module';
import { ImagesModule } from '../modules/images/images.module';
import { databaseConfig } from '../config/database.config';

@Module({
  imports: [
    TypeOrmModule.forRoot(databaseConfig),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '../images'),
      serveRoot: '/',
      serveStaticOptions: {
        index: false,
        fallthrough: true,
        dotfiles: 'allow'
      }
    }),
    NewsModule,
    PartyModule,
    HealthModule,
    MenusModule,
    ImagesModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}

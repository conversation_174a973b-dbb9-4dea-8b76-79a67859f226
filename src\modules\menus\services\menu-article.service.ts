import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MenuArticle } from '../entities/menu-article.entity';
import { MenuArticleDto, MenuArticleListDto, CreateMenuArticleDto, UpdateMenuArticleDto } from '../dto/menu-article.dto';

@Injectable()
export class MenuArticleService {
  constructor(
    @InjectRepository(MenuArticle)
    private menuArticleRepository: Repository<MenuArticle>,
  ) {}

  async findAll(): Promise<MenuArticleDto[]> {
    try {
      const articles = await this.menuArticleRepository.find({
        order: {
          createdAt: 'DESC',
        },
      });
      return articles;
    } catch (error) {
      console.error('获取菜单文章列表失败:', error);
      return [];
    }
  }

  async findOne(id: number): Promise<MenuArticle> {
    try {
      const article = await this.menuArticleRepository.findOne({ where: { id } });
      if (!article) {
        throw new NotFoundException(`ID为${id}的菜单文章不存在`);
      }
      return article;
    } catch (error) {
      console.error(`获取ID为${id}的菜单文章失败:`, error);
      throw error;
    }
  }

  async findByMenuId(menuId: number): Promise<MenuArticleListDto[]> {
    try {
      const articles = await this.menuArticleRepository.find({
        where: { menuId },
        order: {
          createdAt: 'DESC',
        },
        // 选择需要的字段，不包含content
        select: ['id', 'menuId', 'title', 'subtitle', 'author', 'coverImageId', 'createdAt', 'updatedAt']
      });

      // 将实体对象转换为不包含content的DTO对象
      const articleListDtos: MenuArticleListDto[] = articles.map(article => ({
        id: article.id,
        menuId: article.menuId,
        title: article.title,
        subtitle: article.subtitle,
        author: article.author,
        coverImageId: article.coverImageId,
        createdAt: article.createdAt,
        updatedAt: article.updatedAt
      }));

      return articleListDtos;
    } catch (error) {
      console.error(`获取菜单ID为${menuId}的文章列表失败:`, error);
      return [];
    }
  }

  async create(createMenuArticleDto: CreateMenuArticleDto): Promise<MenuArticle> {
    try {
      const newArticle = this.menuArticleRepository.create(createMenuArticleDto);
      return await this.menuArticleRepository.save(newArticle);
    } catch (error) {
      console.error('创建菜单文章失败:', error);
      throw error;
    }
  }

  async update(id: number, updateMenuArticleDto: UpdateMenuArticleDto): Promise<MenuArticle> {
    try {
      // 直接使用repository的findOne方法获取实体对象
      const article = await this.menuArticleRepository.findOne({ where: { id } });
      if (!article) {
        throw new NotFoundException(`ID为${id}的菜单文章不存在`);
      }
      const updatedArticle = Object.assign(article, updateMenuArticleDto);
      return await this.menuArticleRepository.save(updatedArticle);
    } catch (error) {
      console.error(`更新ID为${id}的菜单文章失败:`, error);
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    try {
      // 直接使用repository的findOne方法获取实体对象
      const article = await this.menuArticleRepository.findOne({ where: { id } });
      if (!article) {
        throw new NotFoundException(`ID为${id}的菜单文章不存在`);
      }
      await this.menuArticleRepository.remove(article);
    } catch (error) {
      console.error(`删除ID为${id}的菜单文章失败:`, error);
      throw error;
    }
  }
}

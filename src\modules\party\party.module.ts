import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PartyCategoryController } from './controllers/party-category.controller';
import { PartyArticleController } from './controllers/party-article.controller';
import { PartyCategoryService } from './services/party-category.service';
import { PartyArticleService } from './services/party-article.service';
import { PartyCategory } from './entities/party-category.entity';
import { PartyArticle } from './entities/party-article.entity';

@Module({
  imports: [TypeOrmModule.forFeature([PartyCategory, PartyArticle])],
  controllers: [PartyCategoryController, PartyArticleController],
  providers: [PartyCategoryService, PartyArticleService],
  exports: [PartyCategoryService, PartyArticleService],
})
export class PartyModule {}

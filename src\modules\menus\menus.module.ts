import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MenuController } from './controllers/menu.controller';
import { MenuService } from './services/menu.service';
import { Menu } from './entities/menu.entity';
import { MenuArticle } from './entities/menu-article.entity';
import { MenuArticleController } from './controllers/menu-article.controller';
import { MenuArticleService } from './services/menu-article.service';

@Module({
  imports: [TypeOrmModule.forFeature([Menu, MenuArticle])],
  controllers: [MenuController, MenuArticleController],
  providers: [MenuService, MenuArticleService],
  exports: [MenuService, MenuArticleService],
})
export class MenusModule {}

import { Controller, Get, Param, ParseIntPipe, NotFoundException } from '@nestjs/common';
import { PartyArticleService } from '../services/party-article.service';
import { PartyArticleDto, PartyArticleListItemDto } from '../dto/party-article.dto';

@Controller('api/party-articles')
export class PartyArticleController {
  constructor(private readonly partyArticleService: PartyArticleService) {}

  @Get()
  async findAll(): Promise<{ success: boolean; data: PartyArticleListItemDto[] }> {
    try {
      const articles = await this.partyArticleService.findAll();
      return {
        success: true,
        data: articles,
      };
    } catch (error) {
      console.error('获取党建文章列表失败:', error);
      throw error;
    }
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ success: boolean; data: PartyArticleDto }> {
    try {
      const article = await this.partyArticleService.findOne(id);
      
      return {
        success: true,
        data: article,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`获取党建文章详情失败:`, error);
      throw error;
    }
  }
}

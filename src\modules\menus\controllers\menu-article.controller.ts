import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { MenuArticleService } from '../services/menu-article.service';
import { MenuArticleDto, MenuArticleListDto, CreateMenuArticleDto, UpdateMenuArticleDto } from '../dto/menu-article.dto';

@Controller('api/menu-articles')
export class MenuArticleController {
  constructor(private readonly menuArticleService: MenuArticleService) {}

  @Get()
  async findAll(): Promise<{ success: boolean; data: MenuArticleDto[] }> {
    try {
      const articles = await this.menuArticleService.findAll();
      return {
        success: true,
        data: articles,
      };
    } catch (error) {
      console.error('获取菜单文章列表失败:', error);
      throw error;
    }
  }

  @Get('menu/:menuId')
  async findByMenuId(@Param('menuId') menuId: number): Promise<{ success: boolean; data: MenuArticleListDto[] }> {
    try {
      const articles = await this.menuArticleService.findByMenuId(menuId);
      return {
        success: true,
        data: articles,
      };
    } catch (error) {
      console.error(`获取菜单ID为${menuId}的文章列表失败:`, error);
      throw error;
    }
  }

  @Get(':id')
  async findOne(@Param('id') id: number): Promise<{ success: boolean; data: MenuArticleDto }> {
    try {
      const article = await this.menuArticleService.findOne(id);
      // 将实体对象转换为DTO对象
      const articleDto: MenuArticleDto = {
        id: article.id,
        menuId: article.menuId,
        title: article.title,
        subtitle: article.subtitle,
        content: article.content,
        author: article.author,
        coverImageId: article.coverImageId,
        createdAt: article.createdAt,
        updatedAt: article.updatedAt
      };
      return {
        success: true,
        data: articleDto,
      };
    } catch (error) {
      console.error(`获取ID为${id}的菜单文章失败:`, error);
      throw error;
    }
  }

  @Post()
  async create(@Body() createMenuArticleDto: CreateMenuArticleDto): Promise<{ success: boolean; data: MenuArticleDto }> {
    try {
      const newArticle = await this.menuArticleService.create(createMenuArticleDto);
      // 将实体对象转换为DTO对象
      const articleDto: MenuArticleDto = {
        id: newArticle.id,
        menuId: newArticle.menuId,
        title: newArticle.title,
        subtitle: newArticle.subtitle,
        content: newArticle.content,
        author: newArticle.author,
        coverImageId: newArticle.coverImageId,
        createdAt: newArticle.createdAt,
        updatedAt: newArticle.updatedAt
      };
      return {
        success: true,
        data: articleDto,
      };
    } catch (error) {
      console.error('创建菜单文章失败:', error);
      throw error;
    }
  }

  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateMenuArticleDto: UpdateMenuArticleDto,
  ): Promise<{ success: boolean; data: MenuArticleDto }> {
    try {
      const updatedArticle = await this.menuArticleService.update(id, updateMenuArticleDto);
      // 将实体对象转换为DTO对象
      const articleDto: MenuArticleDto = {
        id: updatedArticle.id,
        menuId: updatedArticle.menuId,
        title: updatedArticle.title,
        subtitle: updatedArticle.subtitle,
        content: updatedArticle.content,
        author: updatedArticle.author,
        coverImageId: updatedArticle.coverImageId,
        createdAt: updatedArticle.createdAt,
        updatedAt: updatedArticle.updatedAt
      };
      return {
        success: true,
        data: articleDto,
      };
    } catch (error) {
      console.error(`更新ID为${id}的菜单文章失败:`, error);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<{ success: boolean; message: string }> {
    try {
      await this.menuArticleService.remove(id);
      return {
        success: true,
        message: `ID为${id}的菜单文章已成功删除`,
      };
    } catch (error) {
      console.error(`删除ID为${id}的菜单文章失败:`, error);
      throw error;
    }
  }
}
